<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Video extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'youtube_video_id',
        'duration',
        'category_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'category_id' => 'integer',
        ];
    }

    /**
     * Get the category that owns the video.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the content record associated with this video.
     */
    public function content(): MorphOne
    {
        return $this->morphOne(Content::class, 'contentable');
    }

    /**
     * Get the YouTube embed URL for this video.
     */
    public function embedUrl(): Attribute
    {
        return Attribute::make(
            get: fn() => "https://www.youtube.com/embed/{$this->youtube_video_id}"
        );
    }

    /**
     * Get the YouTube watch URL for this video.
     */
    public function watchUrl(): Attribute
    {
        return Attribute::make(
            get: fn() => "https://www.youtube.com/watch?v={$this->youtube_video_id}"
        );
    }

    /**
     * Get the YouTube thumbnail URL for this video.
     */
    public function thumbnailUrl(): Attribute
    {
        return Attribute::make(
            get: fn() => "https://img.youtube.com/vi/{$this->youtube_video_id}/maxresdefault.jpg"
        );
    }

    /**
     * Get the YouTube thumbnail URL (medium quality) for this video.
     */
    public function thumbnailMediumUrl(): Attribute
    {
        return Attribute::make(
            get: fn() => "https://img.youtube.com/vi/{$this->youtube_video_id}/mqdefault.jpg"
        );
    }

    /**
     * Extract YouTube video ID from a YouTube URL.
     */
    public static function extractVideoId(string $url): ?string
    {
        $patterns = [
            '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/',
            '/youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }

    /**
     * Validate if a string is a valid YouTube URL.
     */
    public static function isValidYouTubeUrl(string $url): bool
    {
        return self::extractVideoId($url) !== null;
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeInCategory($query, int $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }
}
