<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContentResource\Pages;
use App\Models\Content;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ContentResource extends Resource
{
    protected static ?string $model = Content::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-duplicate';

    public static function getNavigationLabel(): string
    {
        return __('contents');
    }

    public static function getModelLabel(): string
    {
        return __('content');
    }

    public static function getPluralModelLabel(): string
    {
        return __('contents');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label(__('title'))
                    ->placeholder(__('content_title_placeholder'))
                    ->helperText(__('content_title_help'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->label(__('description'))
                    ->placeholder(__('content_description_placeholder'))
                    ->helperText(__('content_description_help'))
                    ->rows(3)
                    ->maxLength(1000),
                Forms\Components\Select::make('category_id')
                    ->label(__('category'))
                    ->placeholder(__('select_category'))
                    ->helperText(__('content_category_help'))
                    ->relationship('category', 'name', function (Builder $query) {
                        return $query->where('status', true);
                    })
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Select::make('contentable_type')
                    ->label(__('content_type'))
                    ->placeholder(__('select_content_type'))
                    ->helperText(__('content_type_help'))
                    ->options([
                        'App\\Models\\Audio' => __('audio'),
                        'App\\Models\\Video' => __('video'),
                        'App\\Models\\Book' => __('book'),
                    ])
                    ->required(),
                Forms\Components\Toggle::make('status')
                    ->label(__('status'))
                    ->helperText(__('content_status_help'))
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label(__('title'))
                    ->searchable()
                    ->sortable()
                    ->limit(40)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 40) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->label(__('description'))
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('category.name')
                    ->label(__('category'))
                    ->badge()
                    ->color('success')
                    ->sortable(),
                Tables\Columns\TextColumn::make('contentable_type')
                    ->label(__('content_type'))
                    ->formatStateUsing(fn(string $state): string => class_basename($state))
                    ->badge()
                    ->color(fn(string $state): string => match (class_basename($state)) {
                        'Audio' => 'primary',
                        'Video' => 'success',
                        'Book' => 'warning',
                        default => 'gray',
                    })
                    ->sortable(),
                Tables\Columns\IconColumn::make('status')
                    ->label(__('status'))
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label(__('category'))
                    ->relationship('category', 'name', function (Builder $query) {
                        return $query->where('status', true);
                    })
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('contentable_type')
                    ->label(__('content_type'))
                    ->options([
                        'App\\Models\\Audio' => __('audio'),
                        'App\\Models\\Video' => __('video'),
                        'App\\Models\\Book' => __('book'),
                    ]),
                Tables\Filters\TernaryFilter::make('status')
                    ->label(__('status'))
                    ->boolean()
                    ->trueLabel(__('active'))
                    ->falseLabel(__('inactive'))
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading(__('delete_confirmation'))
                    ->successNotificationTitle(__('content_deleted')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading(__('bulk_delete_confirmation'))
                        ->successNotificationTitle(__('content_bulk_deleted')),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContents::route('/'),
            'create' => Pages\CreateContent::route('/create'),
            'edit' => Pages\EditContent::route('/{record}/edit'),
        ];
    }
}
