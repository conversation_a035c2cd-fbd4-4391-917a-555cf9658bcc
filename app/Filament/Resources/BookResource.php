<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BookResource\Pages;
use App\Models\Book;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class BookResource extends Resource
{
    protected static ?string $model = Book::class;

    protected static ?string $navigationIcon = 'heroicon-o-book-open';

    public static function getNavigationLabel(): string
    {
        return __('books');
    }

    public static function getModelLabel(): string
    {
        return __('book');
    }

    public static function getPluralModelLabel(): string
    {
        return __('books');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label(__('title'))
                    ->placeholder(__('book_title_placeholder'))
                    ->helperText(__('book_title_help'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->label(__('description'))
                    ->placeholder(__('book_description_placeholder'))
                    ->helperText(__('book_description_help'))
                    ->rows(3)
                    ->maxLength(1000),
                Forms\Components\TextInput::make('pages_count')
                    ->label(__('pages_count'))
                    ->placeholder(__('pages_count_placeholder'))
                    ->helperText(__('pages_count_help'))
                    ->numeric()
                    ->minValue(1),
                Forms\Components\DatePicker::make('published_date')
                    ->label(__('published_date'))
                    ->placeholder(__('published_date_placeholder'))
                    ->helperText(__('published_date_help'))
                    ->native(false),
                Forms\Components\TextInput::make('publisher')
                    ->label(__('publisher'))
                    ->placeholder(__('publisher_placeholder'))
                    ->helperText(__('publisher_help'))
                    ->maxLength(255),
                Forms\Components\Select::make('category_id')
                    ->label(__('category'))
                    ->placeholder(__('select_category'))
                    ->helperText(__('book_category_help'))
                    ->relationship('category', 'name', function (Builder $query) {
                        return $query->where('status', true);
                    })
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\FileUpload::make('cover_image')
                    ->label(__('cover_image'))
                    ->helperText(__('cover_image_help'))
                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                    ->directory('books/covers')
                    ->image()
                    ->imageResizeMode('cover')
                    ->imageCropAspectRatio('3:4')
                    ->imageResizeTargetWidth('300')
                    ->imageResizeTargetHeight('400'),
                Forms\Components\FileUpload::make('file')
                    ->label(__('book_file'))
                    ->helperText(__('book_file_help'))
                    ->acceptedFileTypes(['application/pdf', 'application/epub+zip'])
                    ->directory('books/files'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('cover_image')
                    ->label(__('cover_image'))
                    ->size(60)
                    ->square()
                    ->defaultImageUrl(asset('assets/images/book-placeholder.png')),
                Tables\Columns\TextColumn::make('title')
                    ->label(__('title'))
                    ->searchable()
                    ->sortable()
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->label(__('description'))
                    ->limit(40)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 40) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('category.name')
                    ->label(__('category'))
                    ->badge()
                    ->color('success')
                    ->sortable(),
                Tables\Columns\TextColumn::make('pages_count')
                    ->label(__('pages_count'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('published_date')
                    ->label(__('published_date'))
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('publisher')
                    ->label(__('publisher'))
                    ->limit(20)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 20) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\IconColumn::make('has_file')
                    ->label(__('has_file'))
                    ->boolean()
                    ->getStateUsing(fn(Book $record): bool => $record->hasFile()),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label(__('category'))
                    ->relationship('category', 'name', function (Builder $query) {
                        return $query->where('status', true);
                    })
                    ->searchable()
                    ->preload(),
                Tables\Filters\Filter::make('has_cover_image')
                    ->label(__('has_cover_image'))
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('cover_image')),
                Tables\Filters\Filter::make('has_file')
                    ->label(__('has_file'))
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('file')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading(__('delete_confirmation'))
                    ->successNotificationTitle(__('book_deleted')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading(__('bulk_delete_confirmation'))
                        ->successNotificationTitle(__('books_bulk_deleted')),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBooks::route('/'),
            'create' => Pages\CreateBook::route('/create'),
            'edit' => Pages\EditBook::route('/{record}/edit'),
        ];
    }
}
