<?php

namespace App\Filament\Resources\ContentResource\Pages;

use App\Filament\Resources\ContentResource;
use App\Models\Audio;
use App\Models\Book;
use App\Models\Video;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContent extends EditRecord
{
    protected static string $resource = ContentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load contentable data into the form
        if ($this->record->contentable) {
            $data['contentable'] = $this->record->contentable->toArray();
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Extract contentable data
        $contentableData = $data['contentable'] ?? [];
        unset($data['contentable']);

        // Update the contentable model
        if (!empty($contentableData) && $this->record->contentable) {
            // Add category_id to contentable data
            $contentableData['category_id'] = $data['category_id'];
            $this->record->contentable->update($contentableData);
        }

        return $data;
    }
}
