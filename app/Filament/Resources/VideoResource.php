<?php

namespace App\Filament\Resources;

use App\Filament\Resources\VideoResource\Pages;
use App\Models\Video;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class VideoResource extends Resource
{
    protected static ?string $model = Video::class;

    protected static ?string $navigationIcon = 'heroicon-o-video-camera';

    public static function getNavigationLabel(): string
    {
        return __('videos');
    }

    public static function getModelLabel(): string
    {
        return __('video');
    }

    public static function getPluralModelLabel(): string
    {
        return __('videos');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label(__('title'))
                    ->placeholder(__('video_title_placeholder'))
                    ->helperText(__('video_title_help'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->label(__('description'))
                    ->placeholder(__('video_description_placeholder'))
                    ->helperText(__('video_description_help'))
                    ->rows(3)
                    ->maxLength(1000),
                Forms\Components\TextInput::make('youtube_url')
                    ->label(__('youtube_url'))
                    ->placeholder(__('youtube_url_placeholder'))
                    ->helperText(__('youtube_url_help'))
                    ->url()
                    ->required()
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Forms\Set $set, ?string $state) {
                        if ($state) {
                            $videoId = Video::extractVideoId($state);
                            if ($videoId) {
                                $set('youtube_video_id', $videoId);
                            }
                        }
                    }),
                Forms\Components\TextInput::make('youtube_video_id')
                    ->label(__('youtube_video_id'))
                    ->helperText(__('youtube_video_id_help'))
                    ->required()
                    ->maxLength(11)
                    ->readOnly(),
                Forms\Components\TextInput::make('duration')
                    ->label(__('duration'))
                    ->placeholder(__('duration_placeholder'))
                    ->helperText(__('duration_help'))
                    ->maxLength(10),
                Forms\Components\Select::make('category_id')
                    ->label(__('category'))
                    ->placeholder(__('select_category'))
                    ->helperText(__('video_category_help'))
                    ->relationship('category', 'name', function (Builder $query) {
                        return $query->where('status', true);
                    })
                    ->searchable()
                    ->preload()
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('thumbnail_medium_url')
                    ->label(__('thumbnail'))
                    ->size(80)
                    ->square(),
                Tables\Columns\TextColumn::make('title')
                    ->label(__('title'))
                    ->searchable()
                    ->sortable()
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->label(__('description'))
                    ->limit(40)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 40) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('category.name')
                    ->label(__('category'))
                    ->badge()
                    ->color('primary')
                    ->sortable(),
                Tables\Columns\TextColumn::make('duration')
                    ->label(__('duration'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('youtube_video_id')
                    ->label(__('video_id'))
                    ->copyable()
                    ->copyMessage(__('video_id_copied'))
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label(__('category'))
                    ->relationship('category', 'name', function (Builder $query) {
                        return $query->where('status', true);
                    })
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\Action::make('watch')
                    ->label(__('watch'))
                    ->icon('heroicon-o-play')
                    ->url(fn(Video $record): string => $record->watch_url)
                    ->openUrlInNewTab(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading(__('delete_confirmation'))
                    ->successNotificationTitle(__('video_deleted')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading(__('bulk_delete_confirmation'))
                        ->successNotificationTitle(__('videos_bulk_deleted')),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVideos::route('/'),
            'create' => Pages\CreateVideo::route('/create'),
            'edit' => Pages\EditVideo::route('/{record}/edit'),
        ];
    }
}
