<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'status' => $this->status,
            'parent_id' => $this->parent_id,
            'parent_name' => $this->parent?->name,
            'subcategories_count' => $this->subcategories()->count(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'audio_count' => $this->whenLoaded('audio', function () {
                return $this->audio->count();
            }),
            'videos_count' => $this->whenLoaded('videos', function () {
                return $this->videos->count();
            }),
            'audio' => AudioResource::collection($this->whenLoaded('audio')),
            'videos' => VideoResource::collection($this->whenLoaded('videos')),
        ];
    }
}
