<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VideoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'youtube_video_id' => $this->youtube_video_id,
            'duration' => $this->duration,
            'category_id' => $this->category_id,
            'embed_url' => $this->embed_url,
            'watch_url' => $this->watch_url,
            'thumbnail_url' => $this->thumbnail_url,
            'thumbnail_medium_url' => $this->thumbnail_medium_url,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
