<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Pagination\LengthAwarePaginator;

class CategoryAudioResource extends JsonResource
{
    protected $audioRecords;

    /**
     * Create a new resource instance.
     *
     * @param  mixed  $category
     * @param  LengthAwarePaginator  $audioRecords
     * @return void
     */
    public function __construct($category, LengthAwarePaginator $audioRecords)
    {
        parent::__construct($category);
        $this->audioRecords = $audioRecords;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'category' => [
                'id' => $this->id,
                'name' => $this->name,
                'status' => $this->status,
                'parent_id' => $this->parent_id,
                'parent_name' => $this->parent?->name,
                'created_at' => $this->created_at?->toISOString(),
                'updated_at' => $this->updated_at?->toISOString(),
            ],
            'audio_records' => AudioResource::collection($this->audioRecords->items()),
            'pagination' => [
                'current_page' => $this->audioRecords->currentPage(),
                'last_page' => $this->audioRecords->lastPage(),
                'per_page' => $this->audioRecords->perPage(),
                'total' => $this->audioRecords->total(),
                'from' => $this->audioRecords->firstItem(),
                'to' => $this->audioRecords->lastItem(),
                'has_more_pages' => $this->audioRecords->hasMorePages(),
            ],
            'meta' => [
                'api_version' => 'v1',
                'generated_at' => now()->toISOString(),
            ],
        ];
    }
}
