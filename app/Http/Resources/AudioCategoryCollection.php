<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class AudioCategoryCollection extends ResourceCollection
{
    protected $itemsPerPage;
    protected $itemsPage;

    /**
     * Create a new resource instance.
     *
     * @param  mixed  $resource
     * @param  int  $itemsPerPage
     * @param  int  $itemsPage
     * @return void
     */
    public function __construct($resource, int $itemsPerPage = 10, int $itemsPage = 1)
    {
        parent::__construct($resource);
        $this->itemsPerPage = $itemsPerPage;
        $this->itemsPage = $itemsPage;
    }

    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'categories' => $this->collection->map(function ($category) {
                // Get total audio count for this category
                $totalAudioCount = $category->audio()->count();

                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'status' => $category->status,
                    'parent_id' => $category->parent_id,
                    'parent_name' => $category->parent?->name,
                    'subcategories_count' => $category->subcategories()->count(),
                    'total_audio_count' => $totalAudioCount,
                    'audio_records' => AudioResource::collection($category->audio),
                    'audio_pagination' => [
                        'current_page' => $this->itemsPage,
                        'per_page' => $this->itemsPerPage,
                        'total' => $totalAudioCount,
                        'has_more' => $totalAudioCount > ($this->itemsPage * $this->itemsPerPage),
                    ],
                    'created_at' => $category->created_at?->toISOString(),
                    'updated_at' => $category->updated_at?->toISOString(),
                ];
            }),
            'total_categories' => $this->collection->count(),
            'total_audio_records' => $this->collection->sum(function ($category) {
                return $category->audio()->count();
            }),
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'api_version' => 'v1',
                'generated_at' => now()->toISOString(),
                'items_pagination_info' => [
                    'items_per_page' => $this->itemsPerPage,
                    'items_page' => $this->itemsPage,
                ],
            ],
        ];
    }
}
