<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BookResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'pages_count' => $this->pages_count,
            'published_date' => $this->published_date?->toDateString(),
            'publisher' => $this->publisher,
            'cover_image' => $this->cover_image,
            'cover_image_url' => $this->cover_image_url,
            'file' => $this->file,
            'file_url' => $this->file_url,
            'category_id' => $this->category_id,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
