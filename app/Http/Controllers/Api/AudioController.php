<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AudioCategoryCollection;
use App\Http\Resources\CategoryAudioResource;
use App\Models\Audio;
use App\Models\Category;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AudioController extends Controller
{
    /**
     * Get all audio records grouped by their categories.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAudiosByCategory(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $request->validate([
                'paginated' => 'sometimes|boolean',
                'per_page' => 'sometimes|integer|min:1|max:100',
                'items_per_page' => 'sometimes|integer|min:1|max:50',
                'items_page' => 'sometimes|integer|min:1',
            ]);

            $paginated = $request->boolean('paginated', false);
            $perPage = $request->integer('per_page', 15);
            $itemsPerPage = $request->integer('items_per_page', 10);
            $itemsPage = $request->integer('items_page', 1);

            // Get active categories that have audio content
            $categoriesQuery = Category::query()
                ->where('status', true)
                ->whereHas('audio')
                ->orderBy('name');

            if ($paginated) {
                $categories = $categoriesQuery->paginate($perPage);

                // Load paginated audio for each category
                $categories->getCollection()->load(['audio' => function ($query) use ($itemsPerPage, $itemsPage) {
                    $query->select('id', 'title', 'description', 'audio_file', 'category_id', 'created_at', 'updated_at')
                        ->orderBy('created_at', 'desc')
                        ->skip(($itemsPage - 1) * $itemsPerPage)
                        ->take($itemsPerPage);
                }]);

                return response()->json([
                    'data' => new AudioCategoryCollection($categories->items(), $itemsPerPage, $itemsPage),
                    'pagination' => [
                        'current_page' => $categories->currentPage(),
                        'last_page' => $categories->lastPage(),
                        'per_page' => $categories->perPage(),
                        'total' => $categories->total(),
                        'from' => $categories->firstItem(),
                        'to' => $categories->lastItem(),
                    ],
                ], 200);
            } else {
                $categories = $categoriesQuery->get();

                // Load paginated audio for each category
                $categories->load(['audio' => function ($query) use ($itemsPerPage, $itemsPage) {
                    $query->select('id', 'title', 'description', 'audio_file', 'category_id', 'created_at', 'updated_at')
                        ->orderBy('created_at', 'desc')
                        ->skip(($itemsPage - 1) * $itemsPerPage)
                        ->take($itemsPerPage);
                }]);

                return response()->json([
                    'data' => new AudioCategoryCollection($categories, $itemsPerPage, $itemsPage),
                ], 200);
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error retrieving audio by category: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all(),
            ]);

            return response()->json([
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get audio records for a specific category with pagination.
     *
     * @param Request $request
     * @param int $categoryId
     * @return JsonResponse
     */
    public function getCategoryAudio(Request $request, int $categoryId): JsonResponse
    {
        try {
            // Validate request parameters
            $request->validate([
                'per_page' => 'sometimes|integer|min:1|max:50',
                'page' => 'sometimes|integer|min:1',
            ]);

            $perPage = $request->integer('per_page', 10);
            $page = $request->integer('page', 1);

            // Find the category
            $category = Category::where('id', $categoryId)
                ->where('status', true)
                ->first();

            if (!$category) {
                return response()->json([
                    'error' => 'Category not found or not accessible',
                ], 404);
            }

            // Get paginated audio records for this category
            $audioQuery = Audio::where('category_id', $categoryId)
                ->select('id', 'title', 'description', 'audio_file', 'category_id', 'created_at', 'updated_at')
                ->orderBy('created_at', 'desc');

            $audioRecords = $audioQuery->paginate($perPage, ['*'], 'page', $page);

            return response()->json([
                'data' => new CategoryAudioResource($category, $audioRecords),
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error retrieving category audio: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all(),
                'category_id' => $categoryId,
            ]);

            return response()->json([
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }
}
