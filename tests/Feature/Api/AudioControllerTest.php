<?php

namespace Tests\Feature\Api;

use App\Enums\CategoryType;
use App\Models\Audio;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AudioControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test categories and audio records
        $category1 = Category::create([
            'name' => 'Islamic Lectures',
            'status' => true,
            'category_type' => CategoryType::Audio,
        ]);

        $category2 = Category::create([
            'name' => 'Quran Recitations',
            'status' => true,
            'category_type' => CategoryType::Audio,
        ]);

        // Create inactive category (should not appear in results)
        Category::create([
            'name' => 'Inactive Category',
            'status' => false,
            'category_type' => CategoryType::Audio,
        ]);

        // Create video category (should not appear in audio results)
        Category::create([
            'name' => 'Video Category',
            'status' => true,
            'category_type' => CategoryType::Video,
        ]);

        Audio::create([
            'title' => 'Introduction to Islam',
            'description' => 'A comprehensive introduction to Islamic principles',
            'audio_file' => 'audio/intro_islam.mp3',
            'category_id' => $category1->id,
        ]);

        Audio::create([
            'title' => 'Surah Al-Fatiha',
            'description' => 'Beautiful recitation of the opening chapter',
            'audio_file' => 'audio/surah_fatiha.mp3',
            'category_id' => $category2->id,
        ]);

        Audio::create([
            'title' => 'Another Lecture',
            'description' => 'Another Islamic lecture',
            'audio_file' => 'audio/another_lecture.mp3',
            'category_id' => $category1->id,
        ]);
    }

    public function test_get_audios_by_category_returns_success_response()
    {
        $response = $this->getJson('/api/v1/audios/by-category');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'categories' => [
                        '*' => [
                            'id',
                            'name',
                            'status',
                            'parent_id',
                            'parent_name',
                            'subcategories_count',
                            'total_audio_count',
                            'audio_records' => [
                                '*' => [
                                    'id',
                                    'title',
                                    'description',
                                    'audio_file',
                                    'audio_url',
                                    'category_id',
                                    'created_at',
                                    'updated_at',
                                ]
                            ],
                            'audio_pagination' => [
                                'current_page',
                                'per_page',
                                'total',
                                'has_more',
                            ],
                            'created_at',
                            'updated_at',
                        ]
                    ],
                    'total_categories',
                    'total_audio_records',
                ]
            ]);

        // Verify only active audio categories are returned
        $data = $response->json('data');
        $this->assertEquals(2, $data['total_categories']);
        $this->assertEquals(3, $data['total_audio_records']);
    }

    public function test_get_audios_by_category_with_pagination()
    {
        $response = $this->getJson('/api/v1/audios/by-category?paginated=1&per_page=1');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'pagination' => [
                    'current_page',
                    'last_page',
                    'per_page',
                    'total',
                    'from',
                    'to',
                ]
            ]);

        $pagination = $response->json('pagination');
        $this->assertEquals(1, $pagination['per_page']);
        $this->assertEquals(2, $pagination['total']);
    }

    public function test_validation_errors_for_invalid_parameters()
    {
        $response = $this->getJson('/api/v1/audios/by-category?per_page=150');

        $response->assertStatus(422)
            ->assertJson([
                'errors' => [
                    'per_page' => ['The per page field must not be greater than 100.']
                ]
            ]);
    }

    public function test_empty_response_when_no_audio_categories_exist()
    {
        // Delete all test data
        Audio::truncate();
        Category::truncate();

        $response = $this->getJson('/api/v1/audios/by-category');

        $response->assertStatus(200)
            ->assertJson([
                'data' => [
                    'categories' => [],
                    'total_categories' => 0,
                    'total_audio_records' => 0,
                ]
            ]);
    }

    public function test_audio_pagination_within_categories()
    {
        $response = $this->getJson('/api/v1/audios/by-category?items_per_page=1&items_page=1');

        $response->assertStatus(200);

        $data = $response->json('data');
        $categories = $data['categories'];

        // Find the category with 2 audio records (Islamic Lectures)
        $categoryWithMultipleAudio = collect($categories)->first(function ($category) {
            return $category['total_audio_count'] > 1;
        });

        $this->assertNotNull($categoryWithMultipleAudio);
        $this->assertEquals(1, count($categoryWithMultipleAudio['audio_records']));
        $this->assertEquals(1, $categoryWithMultipleAudio['audio_pagination']['current_page']);
        $this->assertEquals(1, $categoryWithMultipleAudio['audio_pagination']['per_page']);
        $this->assertTrue($categoryWithMultipleAudio['audio_pagination']['has_more']);
    }

    public function test_audio_pagination_validation()
    {
        $response = $this->getJson('/api/v1/audios/by-category?items_per_page=100');

        $response->assertStatus(422)
            ->assertJson([
                'errors' => [
                    'items_per_page' => ['The items per page field must not be greater than 50.']
                ]
            ]);
    }

    public function test_get_category_audio_success()
    {
        $category = Category::factory()->create(['status' => true]);
        Audio::factory(2)->create(['category_id' => $category->id]);

        $response = $this->getJson("/api/v1/audios/category/{$category->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'category' => [
                        'id',
                        'name',
                        'status',
                        'parent_id',
                        'created_at',
                        'updated_at',
                    ],
                    'audio_records' => [
                        '*' => [
                            'id',
                            'title',
                            'description',
                            'audio_file',
                            'audio_url',
                            'category_id',
                            'created_at',
                            'updated_at',
                        ]
                    ],
                    'pagination' => [
                        'current_page',
                        'last_page',
                        'per_page',
                        'total',
                        'from',
                        'to',
                        'has_more_pages',
                    ],
                    'meta',
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals($category->id, $data['category']['id']);
        $this->assertEquals($category->name, $data['category']['name']);
    }

    public function test_get_category_audio_with_pagination()
    {
        $category = Category::factory()->create(['status' => true]);
        Audio::factory(3)->create(['category_id' => $category->id]);

        $response = $this->getJson("/api/v1/audios/category/{$category->id}?per_page=1&page=1");

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertEquals(1, $data['pagination']['per_page']);
        $this->assertEquals(1, $data['pagination']['current_page']);
    }

    public function test_get_category_audio_not_found()
    {
        $response = $this->getJson('/api/v1/audios/category/999');

        $response->assertStatus(404)
            ->assertJson([
                'error' => 'Category not found or not accessible'
            ]);
    }

    public function test_get_category_audio_validation_error()
    {
        $category = Category::factory()->create(['status' => true]);

        $response = $this->getJson("/api/v1/audios/category/{$category->id}?per_page=100");

        $response->assertStatus(422)
            ->assertJson([
                'errors' => [
                    'per_page' => ['The per page field must not be greater than 50.']
                ]
            ]);
    }
}
